import { useState } from 'react';

const Messages = () => {
  const [selectedChat, setSelectedChat] = useState(null);
  const [showChatList, setShowChatList] = useState(true);

  const conversations = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isOnline: false
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
      isOnline: false
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
      isOnline: false
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      isOnline: false
    },
    {
      id: 5,
      name: '<PERSON>',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      isOnline: false
    },
    {
      id: 6,
      name: 'Grégoire Garnier',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/46.jpg',
      isOnline: false
    },
    {
      id: 7,
      name: 'Étienne DeGrasse',
      lastMessage: 'April fool\'s day',
      time: '10 m',
      avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
      isOnline: false
    }
  ];

  const currentChat = {
    id: 1,
    name: 'Guy Hawkins',
    status: 'Online - Last seen, 2:02pm',
    avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    messages: [
      {
        id: 1,
        text: 'Hey',
        time: '10:15 pm',
        sender: 'other',
        avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
      },
      {
        id: 2,
        text: '😊 I\'ve been dealing with imposter syndrome at work, and it\'s impacting my confidence. Have you ever experienced this? How did you overcome it? 🤔',
        time: '12:15 pm',
        sender: 'me',
        avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
      },
      {
        id: 3,
        text: '💡 I find it difficult to speak up in group discussions or meetings. Any suggestions on how to improve my communication skills and become more assertive? 🗣️😊',
        time: '12:15 pm',
        sender: 'me',
        avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
      },
      {
        id: 4,
        text: '🏢 I\'m considering a job offer, but I\'m torn between the opportunity for growth and the fear of leaving my current zone. 😰 Any thoughts? 🤔💭',
        time: '10:15 pm',
        sender: 'other',
        avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
      }
    ]
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-indigo-500 via-purple-500 to-purple-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 pointer-events-none z-10 bg-gradient-to-br from-white/10 to-transparent"></div>

      {/* Main Content */}
      <div className="flex w-full h-screen z-20 relative">
        {/* Left Panel - Conversations List */}
        <div className={`w-96 ${showChatList ? 'flex' : 'hidden'} md:flex bg-white/95 backdrop-blur-xl border-r border-white/20 flex-col shadow-2xl animate-slide-in-left md:w-96 ${showChatList ? 'w-full' : ''}`}>
          {/* Search Bar */}
          <div className="p-5 border-b border-white/20 flex gap-3 items-center bg-gradient-to-r from-white/10 to-white/5 animate-fade-in-down">
            <div className="flex-1 relative flex items-center">
              <svg className="absolute left-3 text-gray-500 z-10" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                placeholder="Search"
                className="w-full py-2.5 pl-10 pr-3 border border-white/30 rounded-xl text-sm outline-none bg-white/90 backdrop-blur-sm transition-all duration-300 shadow-lg focus:border-blue-400 focus:shadow-blue-200/30 focus:shadow-2xl focus:-translate-y-0.5"
              />
            </div>
            <button className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 border-0 rounded-xl text-white cursor-pointer flex items-center justify-center transition-all duration-300 shadow-lg shadow-blue-400/40 relative overflow-hidden hover:from-indigo-600 hover:to-purple-600 hover:-translate-y-0.5 hover:shadow-blue-400/60 hover:shadow-2xl active:translate-y-0 group">
              <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent transition-all duration-500 group-hover:left-full"></div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 5v14M5 12h14"></path>
              </svg>
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto p-0 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {conversations.map((conversation, index) => (
              <div
                key={conversation.id}
                className={`flex items-center p-4 cursor-pointer border-b border-white/10 transition-all duration-300 relative overflow-hidden hover:bg-white/15 hover:translate-x-2 hover:shadow-lg group ${
                  selectedChat === conversation.id
                    ? 'bg-gradient-to-r from-blue-400/20 to-indigo-400/20 border-r-4 border-blue-400 translate-x-2 shadow-lg shadow-blue-400/30'
                    : ''
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
                onClick={() => {
                  setSelectedChat(conversation.id);
                  setShowChatList(false);
                }}
              >
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <img
                  src={conversation.avatar}
                  alt={conversation.name}
                  className="w-12 h-12 rounded-full mr-3 object-cover border-2 border-white/30 transition-all duration-300 shadow-lg group-hover:scale-110 group-hover:border-blue-400 group-hover:shadow-blue-400/40"
                />
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-sm text-gray-800 mb-1">{conversation.name}</div>
                  <div className="text-xs text-gray-500 whitespace-nowrap overflow-hidden text-ellipsis">{conversation.lastMessage}</div>
                </div>
                <div className="text-xs text-gray-500 ml-2">{conversation.time}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Chat Area */}
        <div className={`flex-1 ${!showChatList ? 'flex' : 'hidden'} md:flex flex-col bg-white/95 backdrop-blur-xl shadow-2xl animate-slide-in-right`}>
          {/* Chat Header */}
          <div className="p-5 border-b border-white/20 flex items-center justify-between bg-gradient-to-r from-white/10 to-white/5 animate-fade-in-down">
            <div className="flex items-center">
              <button
                className="md:hidden mr-3 w-8 h-8 flex items-center justify-center text-gray-600 hover:text-blue-500 transition-colors"
                onClick={() => setShowChatList(true)}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M19 12H5M12 19l-7-7 7-7"/>
                </svg>
              </button>
              <img src={currentChat.avatar} alt={currentChat.name} className="w-12 h-12 rounded-full mr-3 object-cover" />
              <div className="flex flex-col">
                <div className="font-semibold text-base text-gray-800 mb-0.5">{currentChat.name}</div>
                <div className="text-xs text-green-600">{currentChat.status}</div>
              </div>
            </div>
            <div className="flex gap-2">
              <button className="w-10 h-10 border-0 bg-white/80 backdrop-blur-sm rounded-xl text-gray-500 cursor-pointer flex items-center justify-center transition-all duration-300 shadow-lg relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-2xl active:translate-y-0 group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </button>
              <button className="w-10 h-10 border-0 bg-white/80 backdrop-blur-sm rounded-xl text-gray-500 cursor-pointer flex items-center justify-center transition-all duration-300 shadow-lg relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-2xl active:translate-y-0 group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="23 7 16 12 23 17 23 7"></polygon>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                </svg>
              </button>
              <button className="w-10 h-10 border-0 bg-white/80 backdrop-blur-sm rounded-xl text-gray-500 cursor-pointer flex items-center justify-center transition-all duration-300 shadow-lg relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-2xl active:translate-y-0 group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="12" cy="5" r="1"></circle>
                  <circle cx="12" cy="19" r="1"></circle>
                </svg>
              </button>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 p-5 overflow-y-auto flex flex-col gap-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {currentChat.messages.map((message) => (
              <div key={message.id} className={`flex items-end gap-2 max-w-[70%] ${message.sender === 'me' ? 'self-end flex-row-reverse' : 'self-start'}`}>
                {message.sender === 'other' && (
                  <img src={message.avatar} alt="User" className="w-8 h-8 rounded-full object-cover" />
                )}
                <div className="flex flex-col gap-1">
                  <div className={`py-3 px-4 rounded-2xl text-sm leading-relaxed break-words ${
                    message.sender === 'me'
                      ? 'bg-blue-500 text-white rounded-br-sm'
                      : 'bg-gray-100 text-gray-800 rounded-bl-sm'
                  }`}>
                    {message.text}
                  </div>
                  <div className={`text-xs text-gray-500 px-1 ${message.sender === 'me' ? 'text-right' : ''}`}>
                    {message.time}
                  </div>
                </div>
                {message.sender === 'me' && (
                  <img src={message.avatar} alt="You" className="w-8 h-8 rounded-full object-cover" />
                )}
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-5 border-t border-white/20 flex gap-3 items-end bg-gradient-to-r from-white/10 to-white/5 animate-fade-in-up">
            <div className="flex-1 relative flex items-center bg-white/90 backdrop-blur-xl rounded-3xl py-2 px-4 shadow-lg border border-white/30 transition-all duration-300 focus-within:shadow-blue-200/30 focus-within:shadow-2xl focus-within:border-blue-400 focus-within:-translate-y-0.5">
              <input
                type="text"
                placeholder="Type your message here..."
                className="flex-1 border-0 bg-transparent py-2 text-sm outline-none resize-none"
              />
              <button className="w-8 h-8 border-0 bg-white/80 backdrop-blur-sm text-gray-500 cursor-pointer rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-sm relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-lg group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                </svg>
              </button>
              <button className="w-8 h-8 border-0 bg-white/80 backdrop-blur-sm text-gray-500 cursor-pointer rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-sm relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-lg group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                  <line x1="9" y1="9" x2="9.01" y2="9"></line>
                  <line x1="15" y1="9" x2="15.01" y2="9"></line>
                </svg>
              </button>
              <button className="w-8 h-8 border-0 bg-white/80 backdrop-blur-sm text-gray-500 cursor-pointer rounded-lg flex items-center justify-center transition-all duration-300 ml-1 shadow-sm relative overflow-hidden hover:bg-blue-400/10 hover:text-blue-400 hover:-translate-y-0.5 hover:shadow-blue-400/30 hover:shadow-lg group">
                <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transition-all duration-500 group-hover:left-full"></div>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"></path>
                </svg>
              </button>
            </div>
            <button className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 border-0 rounded-full text-white cursor-pointer flex items-center justify-center transition-all duration-300 shadow-lg shadow-blue-400/40 relative overflow-hidden hover:from-indigo-600 hover:to-purple-600 hover:-translate-y-0.5 hover:scale-105 hover:shadow-blue-400/60 hover:shadow-2xl active:translate-y-0 active:scale-100 group">
              <div className="absolute top-0 -left-full w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent transition-all duration-500 group-hover:left-full"></div>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Messages;
